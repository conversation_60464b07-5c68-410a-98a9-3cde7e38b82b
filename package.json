{"name": "wealthyhood-web", "version": "0.0.1", "engines": {"node": "20.11.1", "npm": "10.2.4"}, "description": "The Wealthyhood web investment platform", "private": true, "scripts": {"start": "npm run serve", "serve": "node dist/server.js", "build": "npm run build-ts && npm run build-client", "build:dev": "npm run build-ts && npm run build-client:dev", "build-and-lint": "npm run build-ts && npm run lint-fix", "build-ts": "tsc -p .", "build-client": "webpack --config webpack.config.development.js", "build-client:dev": "webpack --env development --config webpack.config.development.js", "watch": "concurrently -k -p \"[{name}]\" -n \"Node,Assets\" -c \"cyan.bold,green.bold\" \"npm run watch-node\" \"npm run watch-client\"", "watch-node": "ts-node-dev --respawn --debug --transpile-only --inspect=0.0.0.0:9229 -- server.ts", "watch-assets": "webpack --config webpack.config.old.js -w", "watch-client": "webpack --env development --config webpack.config.development.js -w", "lint-fix": "tsc --noEmit && eslint \"**/*.{js,ts}\" --quiet --fix", "lint": "tsc --noEmit && eslint \"**/*.{js,ts}\"", "test": "jest --forceExit --useStderr --detectO<PERSON>Handles", "prepare": "husky install"}, "dependencies": {"@datadog/browser-rum": "^4.11.2", "@fortawesome/fontawesome-free": "^5.14.0", "@fortawesome/fontawesome-svg-core": "^1.2.30", "@fortawesome/free-solid-svg-icons": "^5.14.0", "@fortawesome/react-fontawesome": "^0.1.11", "@sentry/integrations": "^7.13.0", "@sentry/node": "^7.13.0", "@sentry/react": "^7.13.0", "@sentry/tracing": "^7.13.0", "@stripe/react-stripe-js": "^2.4.0", "@stripe/stripe-js": "^2.2.2", "@sumsub/websdk": "^2.3.8", "@types/bson": "^4.0.5", "@uppy/aws-s3": "^1.7.2", "@uppy/core": "^1.14.0", "@uppy/react": "^1.10.10", "@wealthyhood/shared-configs": "^1.14.37", "@webcarrot/xirr": "^1.0.3", "auth0": "^4.18.0", "axios": "^1.8.2", "body-parser": "~1.20.2", "chart.js": "^3.9.1", "chartjs-plugin-datalabels": "^2.2.0", "configcat-js-ssr": "^7.1.2", "connect-flash": "^0.1.1", "connect-mongo": "^4.4.1", "cookie-parser": "^1.4.6", "crypto-js": "^4.2.0", "debug": "~2.6.9", "decimal.js": "^10.3.1", "disposable-email-domains": "^1.0.50", "dompurify": "^3.2.4", "dotenv": "^6.0.0", "express": "^4.21.2", "express-openid-connect": "^2.7.2", "express-session": "^1.17.1", "handlebars": "^4.7.7", "libhoney": "^2.0.1", "logrocket": "^2.1.2", "luxon": "^3.4.4", "marked-react": "^2.0.0", "morgan": "~1.9.0", "nanoid": "^3.3.8", "nouislider-react": "^3.4.1", "postmark": "^4.0.5", "pug": "^3.0.3", "qs": "^6.11.0", "react": "^16.14.0", "react-bootstrap": "^1.3.0", "react-chartjs-2": "^4.3.1", "react-confetti": "^6.0.1", "react-dom": "^16.14.0", "react-fontawesome": "^1.7.1", "react-intercom": "^1.0.15", "react-select": "^5.8.0", "react-step-wizard": "^5.3.5", "react-text-mask": "^5.4.3", "react-transition-group": "^4.4.1", "react-world-flags": "^1.6.0", "serve-favicon": "~2.4.5", "stripe": "^14.10.0", "text-mask-addons": "^3.8.0", "truelayer-client": "^1.3.2", "ua-parser-js": "^1.0.33", "universal-cookie": "^4.0.4", "validator": "^13.7.0", "winston": "^3.3.3"}, "devDependencies": {"@sentry/types": "^7.13.0", "@types/auth0": "^2.34.6", "@types/chart.js": "^2.9.24", "@types/connect-flash": "0.0.35", "@types/cookie-parser": "^1.4.3", "@types/crypto-js": "^4.0.1", "@types/csvtojson": "^1.1.5", "@types/debug": "^4.1.5", "@types/disposable-email-domains": "^1.0.1", "@types/express": "^4.17.13", "@types/express-session": "^1.17.3", "@types/faker": "^5.5.8", "@types/jest": "^27.0.1", "@types/luxon": "^3.4.2", "@types/markdown-it": "0.0.9", "@types/mongodb": "^3.6.20", "@types/morgan": "^1.7.37", "@types/node": "^20.5.7", "@types/qs": "^6.9.5", "@types/react": "^16.9.46", "@types/react-dom": "^16.9.8", "@types/react-text-mask": "^5.4.6", "@types/react-transition-group": "^4.4.1", "@types/react-world-flags": "^1.4.5", "@types/serve-static": "^1.13.10", "@types/text-mask-addons": "^3.8.1", "@types/ua-parser-js": "^0.7.36", "@types/validator": "^10.11.3", "@typescript-eslint/eslint-plugin": "^5.2.0", "@typescript-eslint/parser": "^5.2.0", "autoprefixer": "^10.4.16", "concurrently": "^3.5.0", "dotenv-webpack": "^7.0.3", "eslint": "^8.1.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-cypress": "^2.7.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-react": "^7.30.0", "faker": "^5.5.3", "husky": "^7.0.2", "jest": "^27.0.6", "prettier": "^2.4.1", "ts-jest": "^27.0.5", "ts-loader": "^8.4.0", "ts-node-dev": "^1.1.6", "typescript": "^4.9.3", "webpack": "^5.94.0", "webpack-cli": "^4.8.0"}}