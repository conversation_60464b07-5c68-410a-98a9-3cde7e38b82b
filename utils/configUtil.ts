import {
  countriesConfig,
  entitiesConfig,
  investmentUniverseConfig,
  localeConfig,
  plansConfig,
  savingsUniverseConfig,
  taxResidencyConfig
} from "@wealthyhood/shared-configs";
import { PartialRecord } from "../react-views/types/utils";
import { EU_EMAILS } from "../config/usersConfig";

const { getPriceConfig, getPlanConfig } = plansConfig;
const { INVESTMENT_UNIVERSE_CONFIG } = investmentUniverseConfig;
const { COUNTRIES_REGIONS_MAPPING } = countriesConfig;
const { REGIONS_TAX_RESIDENCY_MAPPING } = taxResidencyConfig;

export type InvestmentUniverseAssets = Record<
  investmentUniverseConfig.AssetType,
  investmentUniverseConfig.AssetConfigType
>;

export type SavingsProductUniverse = {
  universe: PartialRecord<
    savingsUniverseConfig.SavingsProductType,
    savingsUniverseConfig.SavingsProductConfigType
  >;
  promotionalSavingsProduct: savingsUniverseConfig.SavingsProductType;
};

export default class ConfigUtil {
  /**
   * PUBLIC METHODS
   */
  public static getPricing(
    companyEntity = entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK
  ): Record<plansConfig.PriceType, plansConfig.PriceConfigType> {
    return getPriceConfig(companyEntity);
  }

  public static getPlans(
    companyEntity = entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK
  ): Record<plansConfig.PlanType, plansConfig.PlanConfigType> {
    return getPlanConfig(companyEntity);
  }

  public static getActiveOnlyInvestmentUniverseAssets(
    companyEntity = entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK
  ): InvestmentUniverseAssets {
    return ConfigUtil._getActiveUniverse(INVESTMENT_UNIVERSE_CONFIG[companyEntity]);
  }

  public static getInvestmentUniverseAssets(
    companyEntity = entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK
  ): InvestmentUniverseAssets {
    return INVESTMENT_UNIVERSE_CONFIG[companyEntity];
  }

  public static getAssetClasses(
    companyEntity = entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK,
    selectableOnly = false
  ): PartialRecord<investmentUniverseConfig.AssetClassType, investmentUniverseConfig.AssetClassConfigType> {
    if (selectableOnly) {
      return Object.fromEntries(
        Object.entries(investmentUniverseConfig.ASSET_CLASS_CONFIG[companyEntity]).filter(
          ([, assetClassConfig]) => assetClassConfig.selectable
        )
      ) as PartialRecord<investmentUniverseConfig.AssetClassType, investmentUniverseConfig.AssetClassConfigType>;
    }
    return investmentUniverseConfig.ASSET_CLASS_CONFIG[companyEntity];
  }

  public static getSectors(
    companyEntity = entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK
  ): PartialRecord<
    investmentUniverseConfig.InvestmentSectorType,
    investmentUniverseConfig.InvestmentSectorConfigType
  > {
    return investmentUniverseConfig.SECTOR_CONFIG[companyEntity];
  }

  public static getGeographies(
    companyEntity = entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK
  ): PartialRecord<
    investmentUniverseConfig.InvestmentGeographyType,
    investmentUniverseConfig.InvestmentGeographyConfigType
  > {
    return investmentUniverseConfig.GEOGRAPHY_CONFIG[companyEntity];
  }

  public static getSavingsProductUniverse(
    companyEntity = entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK
  ): SavingsProductUniverse {
    return savingsUniverseConfig.SAVINGS_UNIVERSE_CONFIG[companyEntity];
  }

  public static getDefaultUserLocale(country: countriesConfig.CountryCodesType): localeConfig.LocaleType {
    return localeConfig.COUNTRY_LOCALE_MAPPINGS[country];
  }

  public static getTaxResidencyConfig(
    country: countriesConfig.CountryCodesType
  ): taxResidencyConfig.TaxResidencyConfigType {
    const region = ConfigUtil._getRegion(country);

    return REGIONS_TAX_RESIDENCY_MAPPING[region];
  }

  public static isResidencyCountryAvailable(
    isUserEuWhitelisted: boolean,
    countryCode: countriesConfig.CountryCodesType
  ): boolean {
    const supportedRegions = isUserEuWhitelisted ? ["UK", "GREECE", "EU"] : ["UK"];

    return supportedRegions.includes(COUNTRIES_REGIONS_MAPPING[countryCode]);
  }

  public static isWaitlistCountry(
    isUserEuWhitelisted: boolean,
    countryCode: countriesConfig.CountryCodesType
  ): boolean {
    if (isUserEuWhitelisted) {
      return false;
    }

    const waitlistRegions = ["GREECE", "EU"];

    return waitlistRegions.includes(COUNTRIES_REGIONS_MAPPING[countryCode]);
  }

  /**
   * PRIVATE METHODS
   */
  private static _getActiveUniverse(
    universeConfig: Record<investmentUniverseConfig.AssetType, investmentUniverseConfig.AssetConfigType>
  ): Record<investmentUniverseConfig.AssetType, investmentUniverseConfig.AssetConfigType> {
    return Object.fromEntries(
      Object.entries(universeConfig).filter(([assetId, assetConfig]) => !assetConfig.deprecated)
    ) as Record<investmentUniverseConfig.AssetType, investmentUniverseConfig.AssetConfigType>;
  }

  private static _getRegion(country: countriesConfig.CountryCodesType): countriesConfig.RegionsType {
    return COUNTRIES_REGIONS_MAPPING[country];
  }
}
