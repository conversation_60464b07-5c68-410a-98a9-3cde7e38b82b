import React from "react";
import { countriesConfig } from "@wealthyhood/shared-configs";
import axios from "axios";
import LoadingOnSubmitButton from "./buttons/loadingOnSubmitButton";
import { emitToast } from "../utils/eventService";
import { ToastTypeEnum } from "../configs/toastConfig";
import CountryRow from "./countryRow";
import InfoModal from "./modals/infoModal";
import ConfigUtil from "../../utils/configUtil";
import { GlobalContext, GlobalContextType } from "../contexts/globalContext";
import { COUNTRIES_REGIONS_MAPPING } from "@wealthyhood/shared-configs/dist/countries";
import Flag from "react-world-flags";
import { UserDocument } from "../../models/User";

const pinnedCountries: { code: countriesConfig.CountryCodesType; name: string }[] = [
  { code: "GR", name: "Greece" },
  { code: "GB", name: "United Kingdom" }
];

const restOfPinnedCountries = countriesConfig.availableCountries
  .filter(
    (country) =>
      ["EU"].includes(COUNTRIES_REGIONS_MAPPING[country.code]) &&
      !pinnedCountries.some((pinnedCountry) => pinnedCountry.code === country.code)
  )
  .sort((a, b) => a.name.localeCompare(b.name));

const allPinnedCountries = [...pinnedCountries, ...restOfPinnedCountries];

const nonPinnedCountries = countriesConfig.availableCountries.filter(
  (country) => !allPinnedCountries.some((pinnedCountry) => pinnedCountry.code === country.code)
);

interface CountriesWizardState {
  searchTerm: string;
  selectedCountryCode: countriesConfig.CountryCodesType;
  selectedCountryName: string;
  isSelectedCountrySupported: boolean;
  showUnsupportedCountryInfoModal: boolean;
  showWaitlistCountryInfoModal: boolean;
  user: UserDocument;
}

class CountriesSearch extends React.Component<Record<string, never>, CountriesWizardState> {
  constructor(props: any) {
    super(props);

    this.state = {
      searchTerm: "",
      selectedCountryCode: null,
      selectedCountryName: "",
      isSelectedCountrySupported: false,
      showUnsupportedCountryInfoModal: false,
      showWaitlistCountryInfoModal: false,
      user: {} as UserDocument
    };

    this._handleSearchTermChange = this._handleSearchTermChange.bind(this);
  }

  componentDidMount() {
    this.setState({ user: (this.context as GlobalContextType).user });
  }

  private _setShowUnsupportedCountryInfoModal(showUnsupportedCountryInfoModal: boolean) {
    this.setState({ showUnsupportedCountryInfoModal });
  }

  private _setShowWaitlistCountryInfoModal(showWaitlistCountryInfoModal: boolean) {
    this.setState({ showWaitlistCountryInfoModal });
  }

  private _handleSearchTermChange(event: React.ChangeEvent<HTMLInputElement>) {
    const userInput = event.target.value.trim();
    this.setState({ searchTerm: userInput });
  }

  private async _handleSelectedCountry(countryCode: countriesConfig.CountryCodesType) {
    const { user } = this.state;
    const updatedUser =
      user.residencyCountry === countryCode ? user : await this._submitResidencyCountryAndUpdateUser(countryCode);
    this.setState({ user: updatedUser });

    const selectedCountry = countriesConfig.countries.find((country) => country.code === countryCode);
    const residencyAvailable = ConfigUtil.isResidencyCountryAvailable(
      updatedUser.isEuWhitelisted,
      selectedCountry.code
    );
    const isWaitlistCountry = ConfigUtil.isWaitlistCountry(updatedUser.isEuWhitelisted, selectedCountry.code);

    if (residencyAvailable) {
      this.setState({
        selectedCountryCode: countryCode,
        selectedCountryName: selectedCountry.name,
        isSelectedCountrySupported: true,
        showUnsupportedCountryInfoModal: false,
        showWaitlistCountryInfoModal: false
      });
    } else if (isWaitlistCountry) {
      this.setState({
        selectedCountryCode: countryCode,
        selectedCountryName: selectedCountry.name,
        isSelectedCountrySupported: false,
        showUnsupportedCountryInfoModal: false,
        showWaitlistCountryInfoModal: true
      });
    } else {
      this.setState({
        selectedCountryCode: countryCode,
        selectedCountryName: selectedCountry.name,
        isSelectedCountrySupported: false,
        showUnsupportedCountryInfoModal: true,
        showWaitlistCountryInfoModal: false
      });
    }
  }

  private _submitResidencyCountryAndUpdateUser = async (
    countryCode: countriesConfig.CountryCodesType
  ): Promise<UserDocument> => {
    try {
      await axios({
        method: "POST",
        url: "/investor/residency-country",
        data: {
          residencyCountry: countryCode
        }
      });
      const response = await axios({
        method: "GET",
        url: "/investor/me"
      });
      const updatedUser = response.data as UserDocument;
      return updatedUser;
    } catch (err) {
      emitToast({
        content: "Oops something went wrong!",
        toastType: ToastTypeEnum.error
      });
    }
  };

  private _handleJoinTheWaitlist = async (): Promise<void> => {
    const { selectedCountryCode } = this.state;
    try {
      await axios({
        method: "POST",
        url: "/investor/join-waiting-list",
        data: {
          residencyCountry: selectedCountryCode
        }
      });
      window.location.href = "/investor/waitlist";
    } catch (err) {
      emitToast({
        content: "Oops something went wrong!",
        toastType: ToastTypeEnum.error
      });
    }
  };

  render(): JSX.Element {
    const {
      searchTerm,
      isSelectedCountrySupported,
      selectedCountryCode,
      selectedCountryName,
      showUnsupportedCountryInfoModal,
      showWaitlistCountryInfoModal,
      user
    } = this.state;

    const filteredCountries = countriesConfig.availableCountries.filter(
      (country) =>
        country.code.toLowerCase().startsWith(searchTerm.toLowerCase()) ||
        country.name.toLowerCase().startsWith(searchTerm.toLowerCase())
    );

    return (
      <>
        <div className="search-bar">
          <span className="material-symbols-outlined me-2 search-bar-icon">search</span>
          <input
            className="search-bar-input"
            type="text"
            placeholder="Search your country"
            onChange={this._handleSearchTermChange}
          ></input>
        </div>
        {searchTerm.length > 0 && filteredCountries.length == 0 && (
          <div className="search-results-none">
            <h5 className="search-text-not-found">No results found.</h5>
            <h6 className="search-text-shorten-search">Try shortening your search.</h6>
          </div>
        )}
        <div className="search-results">
          {searchTerm.length === 0 && allPinnedCountries.length && (
            <div id="pinned-countries" className="mt-4 mb-4 border-bottom-countries-list">
              {allPinnedCountries.map((country, key) => {
                return (
                  <CountryRow
                    id={key}
                    countryCode={country.code as countriesConfig.CountryCodesType}
                    countryName={country.name}
                    isSelected={selectedCountryCode === country.code}
                    handleSelectedCountry={() =>
                      this._handleSelectedCountry(country.code as countriesConfig.CountryCodesType)
                    }
                    isAvailable={ConfigUtil.isResidencyCountryAvailable(user.isEuWhitelisted, country.code)}
                    isWaitlistCountry={ConfigUtil.isWaitlistCountry(user.isEuWhitelisted, country.code)}
                    key={key}
                  />
                );
              })}
            </div>
          )}
          {searchTerm.length === 0 && nonPinnedCountries.length > 0 && (
            <div className="mt-4 mb-4 border-bottom-countries-list">
              {nonPinnedCountries.map((country, key) => {
                return (
                  <CountryRow
                    id={key}
                    countryCode={country.code as countriesConfig.CountryCodesType}
                    countryName={country.name}
                    isSelected={selectedCountryCode === country.code}
                    handleSelectedCountry={() =>
                      this._handleSelectedCountry(country.code as countriesConfig.CountryCodesType)
                    }
                    isAvailable={ConfigUtil.isResidencyCountryAvailable(user.isEuWhitelisted, country.code)}
                    isWaitlistCountry={ConfigUtil.isWaitlistCountry(user.isEuWhitelisted, country.code)}
                    key={key}
                  />
                );
              })}
            </div>
          )}

          <div className="mt-4 mb-4">
            {searchTerm.length > 0 &&
              filteredCountries.map((country, key) => {
                return (
                  <CountryRow
                    id={key}
                    countryCode={country.code as countriesConfig.CountryCodesType}
                    countryName={country.name}
                    isSelected={selectedCountryCode === country.code}
                    handleSelectedCountry={() =>
                      this._handleSelectedCountry(country.code as countriesConfig.CountryCodesType)
                    }
                    isAvailable={ConfigUtil.isResidencyCountryAvailable(user.isEuWhitelisted, country.code)}
                    isWaitlistCountry={ConfigUtil.isWaitlistCountry(user.isEuWhitelisted, country.code)}
                    key={key}
                  />
                );
              })}
          </div>
        </div>

        <div className="row p-0 m-0 bg-white h-10 fixed-bottom">
          {/* <!-- Dummy div to follow spacing of layout (fixed right side)--> */}
          <div className="col-md-7 p-0 bg-primary d-none d-sm-block" />
          <div className="col-md-5 p-0 border-top bg-white">
            <div className="row m-0 px-md-5 px-3 h-100 overflow-hidden justify-content-end">
              <div className="d-flex p-0 justify-content-end align-self-center">
                <LoadingOnSubmitButton
                  type="button"
                  className="btn btn-primary w-100 float-end"
                  disabled={!isSelectedCountrySupported}
                  customonclick={async () => {
                    window.location.href = "/investor/id-verification";
                  }}
                >
                  {isSelectedCountrySupported ? "Next" : "Select Residency Country"}
                </LoadingOnSubmitButton>
              </div>
            </div>
          </div>
        </div>
        <InfoModal
          title={null}
          show={showUnsupportedCountryInfoModal}
          handleClose={() => this._setShowUnsupportedCountryInfoModal(false)}
        >
          <h5 className="fw-bold mb-4">We’re coming soon!</h5>
          <p className="text-muted">
            Unfortunately, Wealthyhood is not available in your country yet. Please rest assured, we’re working
            hard to bring Wealthyhood to {selectedCountryName} as soon as possible!
          </p>
        </InfoModal>
        <InfoModal
          title={null}
          show={showWaitlistCountryInfoModal}
          handleClose={() => this._setShowWaitlistCountryInfoModal(false)}
        >
          <h5 className="fw-bold mb-4">
            Get early access! <Flag code={selectedCountryCode} style={{ verticalAlign: "unset" }} height="16px" />
          </h5>
          <p className="text-muted">
            Join the waitlist to secure your spot for early access to the app and earn a free share up to 200€ when
            you open your account!
          </p>
          {selectedCountryCode === "GR" && (
            <p className="text-muted">Do you have a skip-the-line code? Enter it on the next screen!</p>
          )}
          <LoadingOnSubmitButton
            type="button"
            className="btn btn-primary w-100 float-end m-auto mt-4"
            customonclick={() => this._handleJoinTheWaitlist()}
          >
            Join the waitlist!
          </LoadingOnSubmitButton>
        </InfoModal>
      </>
    );
  }
}

CountriesSearch.contextType = GlobalContext;

export default CountriesSearch;
