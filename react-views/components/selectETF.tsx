import React from "react";
import { investmentUniverseConfig } from "@wealthyhood/shared-configs";

type PropsType = {
  defaultValue: string;
  name: string;
} & React.SelectHTMLAttributes<HTMLSelectElement>;

class SelectETF extends React.Component<PropsType> {
  render(): JSX.Element {
    const { defaultValue, name, ...htmlProps } = this.props;

    return (
      <select defaultValue={defaultValue} name={name} className="form-control" {...htmlProps}>
        <option value="">Select</option>
        {investmentUniverseConfig.AssetArrayConst.map((assetKey: investmentUniverseConfig.AssetType) => (
          <option value={assetKey} key={`asset-option-${assetKey}`}>
            {`${investmentUniverseConfig.ASSET_CONFIG[assetKey].simpleName} (${investmentUniverseConfig.ASSET_CONFIG[assetKey].tickerWithCurrency})`}
          </option>
        ))}
      </select>
    );
  }
}

export default SelectETF;
