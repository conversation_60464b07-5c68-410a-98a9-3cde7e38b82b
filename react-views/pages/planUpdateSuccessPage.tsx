import React from "react";
import { PagePropsType } from "../types/page";
import MainLayout from "../layouts/mainLayout";
import { entitiesConfig, plansConfig } from "@wealthyhood/shared-configs";
import { formatDateToDDMONYY } from "../utils/dateUtil";
import ConfigUtil from "../../utils/configUtil";
import { PLAN_ICON_IMAGE_CONFIG } from "../configs/plansConfig";

export type PlanUpdateSuccessPropsType = {
  from?: plansConfig.PriceType;
  to?: plansConfig.PriceType;
} & PagePropsType;

class PlanUpdateSuccessPage extends React.Component<PlanUpdateSuccessPropsType> {
  private _getPlanConfig(): plansConfig.PlanConfigType {
    const { user } = this.props;
    const { expiration, price } = user.subscription;

    const PRICE_CONFIG = ConfigUtil.getPricing(this.props.user.companyEntity);
    const PLAN_CONFIG = ConfigUtil.getPlans(this.props.user.companyEntity);

    const plan = expiration?.downgradesTo
      ? PRICE_CONFIG[expiration.downgradesTo]?.plan
      : PRICE_CONFIG[price]?.plan;

    return PLAN_CONFIG[plan];
  }

  private _getActivePlanName(): string {
    const { user } = this.props;
    const { price } = user.subscription;

    const PRICE_CONFIG = ConfigUtil.getPricing(this.props.user.companyEntity);
    const PLAN_CONFIG = ConfigUtil.getPlans(this.props.user.companyEntity);

    const plan = PRICE_CONFIG[price]?.plan;
    return PLAN_CONFIG[plan].name;
  }

  private _getBody(): string {
    const { user, from, to } = this.props;
    const expirationDate = user.subscription.expiration?.date;
    const { keyName } = this._getPlanConfig();

    const PRICE_CONFIG = ConfigUtil.getPricing(this.props.user.companyEntity);
    const PLAN_CONFIG = ConfigUtil.getPlans(this.props.user.companyEntity);

    // Free will be always the result of downgrade, so expiration date will always exist
    if (keyName == "free") {
      return (
        "Your plan has been switched to Wealthyhood Basic." +
        (expirationDate
          ? `You will continue to enjoy your ${this._getActivePlanName()} account benefits until ${formatDateToDDMONYY(
              expirationDate
            )}.`
          : "")
      );
    }

    if (from && to) {
      if (
        PRICE_CONFIG[from].plan === PRICE_CONFIG[to].plan &&
        PRICE_CONFIG[from].recurrence !== PRICE_CONFIG[to].recurrence
      ) {
        return `Your plan has been switched to ${
          PRICE_CONFIG[to].recurrence === "yearly" ? "annual" : "monthly"
        } billing.`;
      }
    }

    if (keyName == "paid_low" && from === "free_monthly") {
      // User is upgrading from Beginner -> Plus
      return user.companyEntity === entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK
        ? `Your plan has now been upgraded to Wealthyhood ${PLAN_CONFIG.paid_low.name}! You’re all set for zero custody fees, bonus dividends, cashback on investments and more premium features!`
        : `Your plan has now been upgraded to Wealthyhood ${PLAN_CONFIG.paid_low.name}! You’re all set for daily insights, learning, high interest, better FX rates and more premium features!`;
    } else if (keyName === "paid_low") {
      // User is downgrading from Gold -> Plus
      return `Your plan has been switched to Wealthyhood ${PLAN_CONFIG.paid_low.name}.`;
    } else if (keyName === "paid_mid") {
      return user.companyEntity === entitiesConfig.CompanyEntityEnum.WEALTHYHOOD_UK
        ? `Your plan has now been upgraded to Wealthyhood ${PLAN_CONFIG.paid_mid.name}! You’re all set for zero custody fees, even more bonus dividends, cashback on investments and more premium features!`
        : `Your plan has now been upgraded to Wealthyhood ${PLAN_CONFIG.paid_mid.name}! You’re all set for priority customer services, daily insights, learning, even higher interest, better FX rates and more premium features!`;
    }
  }

  render(): JSX.Element {
    const { keyName } = this._getPlanConfig();

    return (
      <MainLayout title={""} user={this.props.user} activePage={this.props.activePage}>
        <div className="wh-card-body fade-in bg-white mb-5 p-md-5 py-5 px-3">
          <div className="row justify-content-center mb-5">
            <div className="col d-flex justify-content-center">
              <img alt="icon" src={PLAN_ICON_IMAGE_CONFIG[keyName]} style={{ width: "88px", height: "88px" }} />
            </div>
          </div>
          <div className="row m-0">
            <div className="col p-0 d-flex flex-column justify-content-center">
              <h5 className="fw-bolder text-center mb-4">You’re all set!</h5>
              <p className="text-center text-muted">{this._getBody()}</p>
              <p className="text-center">
                <a href="/" className="btn btn-primary w-100 mt-5">
                  Got it!
                </a>
              </p>
            </div>
          </div>
        </div>
      </MainLayout>
    );
  }
}

export default PlanUpdateSuccessPage;
