import React from "react";
import { PagePropsType } from "../types/page";
import { emitToast, eventEmitter, EVENTS } from "../utils/eventService";
import SuccessLayout from "../layouts/successLayout";
import axios from "axios";
import { ToastTypeEnum } from "../configs/toastConfig";
import LoadingOnSubmitButton from "../components/buttons/loadingOnSubmitButton";
import ValidationInput from "../components/validationInput";
import IntercomUtil from "../utils/intercomUtil";

export type WaitlistPagePropsType = PagePropsType;

type StateType = {
  referralInput: string;
  showErrorMessage: boolean;
};

class WaitlistPage extends React.Component<WaitlistPagePropsType, StateType> {
  constructor(props: WaitlistPagePropsType) {
    super(props);

    this.state = {
      referralInput: undefined,
      showErrorMessage: false
    };
  }

  private _getReferralCodeFromInput = () => {
    const { referralInput } = this.state;

    return referralInput?.includes("wlthd=") ? referralInput?.split("wlthd=")?.[1] : referralInput;
  };

  private _isReferralCodeValid = () => {
    const referralCode = this._getReferralCodeFromInput();
    const referralCodeRegex = new RegExp("^[a-zA-Z0-9]{8}$");

    return referralCodeRegex.test(referralCode);
  };

  private _submitReferralCodeIfValid = async () => {
    if (!this._isReferralCodeValid()) {
      this.setState({ showErrorMessage: true });
      return;
    }

    const referralCode = this._getReferralCodeFromInput();

    try {
      await axios.post("/investor/set-referrer", {
        referralCode: referralCode
      });

      window.location.href = "/investor/open-account";
    } catch (err) {
      eventEmitter.emit(EVENTS.loadingSplashMask, "");
      emitToast({
        content: err.response.data.message,
        toastType: ToastTypeEnum.error
      });
      // rethrow error in order to restore button which triggers this action
      throw err;
    }
  };

  private _showWaitlistPromo = () => {
    return this.props.user.residencyCountry === "GR";
  };

  render(): JSX.Element {
    const { referralInput, showErrorMessage } = this.state;

    return (
      <SuccessLayout
        activePage={this.props.activePage}
        subtitle={this.props.subtitle}
        title={this.props.title}
        user={this.props.user}
        enableModals={false}
        imageUrl={"/images/icons/astronaut-verified.png"}
        enableIntercom={true}
        actionElement={
          <div className="p-0 fade-in">
            {this._showWaitlistPromo() && (
              <div className="row m-0 mb-4 w-100 text-center">
                {referralInput && referralInput?.length > 0 ? (
                  <LoadingOnSubmitButton
                    style={{ maxWidth: "100% !important" }}
                    type="button"
                    className="btn btn-primary fw-100"
                    customonclick={async () => this._submitReferralCodeIfValid()}
                    enableOnCompletion={true}
                  >
                    Submit
                  </LoadingOnSubmitButton>
                ) : (
                  <button
                    style={{ maxWidth: "100% !important" }}
                    type="button"
                    className="btn btn-primary fw-100"
                    disabled
                  >
                    Submit
                  </button>
                )}
              </div>
            )}
            <div className="row m-0 w-100 text-center">
              <a
                href="#"
                onClick={() => IntercomUtil.show()}
                className="text-primary text-decoration-none"
                style={{ maxWidth: "100% !important" }}
              >
                Chat with us
              </a>
            </div>
          </div>
        }
      >
        <div className="row m-0 p-0 mb-4 mt-4 text-center">
          <h3 className="fade-in fw-bolder">You are in!</h3>
        </div>
        <div className="row m-0 p-0 mb-4">
          <div className="p-0 fade-in">
            <p className="text-muted">
              {this._showWaitlistPromo()
                ? "Keep an eye on your inbox for your early access invite. Got a promo code to skip the line? Add it below! 🫡"
                : "Thank you for joining our waitlist. Keep an eye on your inbox and notifications, as your exclusive early access invitation will be arriving soon. In the meantime, reach out to us with any questions!"}
            </p>
            {this._showWaitlistPromo() && (
              <p className="text-muted">
                Tip: Friends who already have access to Wealthyhood can share a promo code with you.
              </p>
            )}
          </div>
          {this._showWaitlistPromo() && (
            <div className="form-group mt-4 px-0 mx-0 position-relative">
              <ValidationInput
                isValid={() => !referralInput || referralInput?.length === 0 || this._isReferralCodeValid()}
                onChange={(referralInput: any) => {
                  this.setState({ referralInput: referralInput, showErrorMessage: false });
                }}
                value={referralInput}
                placeholder="Enter your promo code here"
                errorMessage="This promo code is invalid."
                required={true}
                showError={showErrorMessage}
              />
            </div>
          )}
        </div>
      </SuccessLayout>
    );
  }
}

export default WaitlistPage;
