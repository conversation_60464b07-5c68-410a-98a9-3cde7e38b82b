import { NextFunction, Response } from "express";
import { CustomRequest } from "custom";
import appApiService, { API_ROUTES } from "../services/appApiService";
import { isVerified } from "../react-views/utils/userUtil";

export const hasNotBeenWelcomedHandler = (req: CustomRequest, res: Response, next: NextFunction): void => {
  if (!req.user.viewedWelcomePage) {
    return next();
  }
  res.redirect("/");
};

export const hasNotSubscribedHandler = (req: CustomRequest, res: Response, next: NextFunction): void => {
  if (!req.user.hasSubscription) {
    return next();
  }
  res.redirect("/");
};

export const hasNotLinkedBankHandler = (req: CustomRequest, res: Response, next: NextFunction): void => {
  if (!req.user.bankAccounts || req.user.bankAccounts.length === 0) {
    return next();
  }
  res.redirect("/");
};

export const isNotVerifiedHandler = (req: CustomRequest, res: Response, next: NextFunction): void => {
  if (!isVerified(req.user)) {
    return next();
  }
  res.redirect("/");
};

export const isVerifiedHandler = (req: CustomRequest, res: Response, next: NextFunction): void => {
  if (isVerified(req.user)) {
    return next();
  }
  res.redirect("/");
};

export const userHasNotDeposit = async (req: CustomRequest, res: Response, next: NextFunction): Promise<void> => {
  const deposits = await appApiService.getM2M(API_ROUTES.transactions.deposits.all(), req.user.id);
  if (deposits.length == 0) {
    return next();
  }
  res.redirect("/");
};

export const hasNotCompletedIdVerificationHandler = (
  req: CustomRequest,
  res: Response,
  next: NextFunction
): void => {
  if (!req.user.hasCompletedKycJourney) {
    return next();
  }
  res.redirect("/");
};
